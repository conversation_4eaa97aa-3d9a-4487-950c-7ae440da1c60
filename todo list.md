# todo list
## Effettiva
- ~~sistemare l'intestazione dell'immagine~~
- ~~sistemare icona unica~~
- ~~sistemare webscraping segnalini~~
- ~~sistemare l'accesso a mazzi~~
- ~~sistemare i popup~~
- ~~sistemare webscraping descrizione leader~~
- ~~sistemare visualizzazione descrizione leader~~
- ~~sistemare la posizion del footer~~
- ~~sistemare la posizione dei messaggi di errore~~
- ~~scrivere i termini di servizio~~
- ~~scrivere le traduzioni~~
- ~~sistemare bordi aggiunta/rimozione carte~~
- ~~sistemare la barra di ricerca nella navbar~~
- ~~sistemare la generazione dell'uscita della carta~~
- ~~aggiustare il tasto di aggiunta carte~~
- ~~impostare i thread per la gestione dell'inserimento carte~~
- ~~migliorare i thread per la gestione dell'inserimento carte~~
- ~~sistemare il footer (link <PERSON><PERSON><PERSON>)~~
- ~~refreshare tutte le carte~~
- ~~far funzionare le mail~~
- ~~sistemare il testo delle mail di aggiunta nuove carte~~
- ~~sistemare l'esistenza dei link next/back nelle carte~~
- ~~creazione di mail alias `<EMAIL>`~~
- ~~filtri di ricerca~~
    - ~~fare in modo che se io apro i filtri avanzati e modifico qualcosa nel caricamento non venga richiusa in automatico la sezione filtri avanzati~~
- ~~aggiustare il popup di aggiunta carte~~
- ~~trasformazione delle analisi delle carte in grafici~~
- ~~gestione della collezione~~
- ~~analisi delle statistiche delle carte nei mazzi~~
    - ~~numero carte x costo e per tipo~~
    - ~~tratti~~
        - ~~divisi~~
        - ~~non divisi~~
    - ~~hp/potenza media~~
    - ~~sistemare la larghezza dei card su mobile~~
- ~~Implementazione di cache per migliorare le performance~~
- ~~ottimizzare il popup di aggiunta carte~~
- ~~Funzionalità di esportazione mazzi in formato TXT e JSON~~
- ~~Funzionalità di importazione mazzi da URL o file~~
    - ~~import da url rotto~~
- ~~aggiornare l'index dei mazzi~~
- ~~limitare le notifiche telegram durante l'invio dei batch~~
- ~~miglioramento pagina utenti per la gestione di admin~~
- ~~Migrazione del sistema di webscraping da Java a Laravel (integrazione nella pagina update) (il progetto java si trova nella cartella WebScrapingStarWars, che è un clone del repository https://github.com/RickyMandich/WebScrapingStarWars.git)~~
- ~~Sistema di versionamento per i mazzi~~
- ~~gestire l'attributo get "name" nella pagina carte per impostare già il filtro, attenzione che si refresha con un oninput o simile, quindi non basta un value="{{ $_GET['nome'] }} nel capo input del filtro del nome~~
- ~~riscrivi completamente l'aggiornamento del DB con nuove carte prendendole direttamente dall'api del sito ufficiale (esempio di api singola carta: "https://admin.starwarsunlimited.com/api/card/" + cid + "?locale=it", puoi trovare un esempio di json nella cartella example, esempio di api elenco carte: "https://admin.starwarsunlimited.com/api/card-list?locale=it&filters[variantOf][id][$null]=true&pagination[page]=" + page + "&pagination[pageSize]=10", puoi trovare un esempio di json nella cartella example), cerca di semplificare e ottimizzare il più possibile il processo mantenendo un sistema di notifiche tramite telegram per comunicare lo stato delle operazioni, gestisci tutto con un'unico messaggio che viene modificato nel tempo e gestisci anche il fatto che venga effettuata una verifica che la carta non sia già presente, manda una mail a tutti gli utenti con le carte aggiunte e agli admin con le carte che hanno lanciato errori/eccezioni o che erano già presenti, inserisci anche il motivo specifico per cui non sono state inserite~~
- ~~creare un pagina admin "errori" che mostra tutti gli errori che si sono verificati e che permette di segnarli come completati, devono venire salvati quando si creano (oltre alla già presente gestione con visualizzazione dettagliata per gli admin e notifica tramite telegram e tramite mail), visto che stiamo creando una nuova pagina bisogna anche aggiungere il relativo pulsante (direi che basta nel pannello admin), decidi tu in base a cosa ti sembra più opportuno se creare una nuova entità nel DB o se gestirlo tramite file json~~
- ~~aggiornare la documentazione~~
- ~~generazione di una guida avanzata e comprensione completa del progetto~~
- ~~aggiungere i pulsanti "segna come ignorato" e "segna come risolto" nell'elenco degli errori e nella mail di comunicazione, nella mail metti anche il link all'errore~~
- ~~aggiungere pulsanti per segnare come risolti o ignorati tutti i nuovi errori, e possibilità di selezione multipla per assegnare stati specifici a errori selezionati~~


## Funzionalità Future
- Funzionalità di condivisione social
- Sistema di tag personalizzati per i mazzi (etichette come "Aggro", "Control", "Budget", "Meta")
- Modalità offline/PWA per consultazione carte (Progressive Web App)
- Sistema di wishlist per carte desiderate
- Funzionalità di deck-building guidato per principianti
- aggiungere alle statistiche del mazzo qual è la percentuale che una carte trovi una carta che soddisfi i requisiti per giocare o pescare carte